<?php

use Shopnc\db\driver\Mysql;
use Upet\Integrates\Redis\RedisManager as Redis;

class syncStaffInfoControl extends BaseCronControl
{
    /**
     * Notes:
     * 1. 更改新手机号时，清空旧手机号的职员信息，更新到新手机号的职员信息
     * 2. 更新138手机号已有的分销订单转到135的手机号
     * 3. 因为shr变更信息都会走mq,则判断是否为修改手机号才处理
     * User: rocky
     * DateTime: 2023/4/3 15:30
     */
    public function indexOp()
    {
        $rs = $this->newShrMQQueue('shr-dc-staff-sync-awen-e', 'shr-dc-staff-sync-awen');
        if (!$rs) {
            return;
        }
        list($cnn, $queue) = $rs;
        // 使用安全的消费方法
        $this->safeConsume($queue, function ($envelope, $q) {
            $msg = $envelope->getBody();
            //判断是否重复数据
            $md5_key = md5($msg);
            $res = Redis::get($md5_key);
            if ($res){
                $q->ack($envelope->getDeliveryTag()); //手动发送ACK应答
                return;
            }else{
                Redis::setex($md5_key,300,1);
            }
            $data = json_decode($msg, true);
            $log = json_encode([
                'name'=>$data['name'],
                'mobile'=>hideStr($data['mobile']),
                'shr_staff_no'=>$data['shr_staff_no'],
                'status'=>$data['status'],
                'nc_code'=>$data['nc_code'],
                'is_change_position'=>$data['is_change_position']
            ], JSON_UNESCAPED_UNICODE);
            $this->log('员工信息通知-data:' . $log);
            $model_member = Model('member');

            try {
                $field = 'chain_user.chain_user_id,chain_user.chain_id, chain_user.user_mobile, chain_user.shr_state, chain_user.shr_staff_no, chain_user.status,
                chain_user.store_phone_state,member.member_id,member.distri_chainid, member.distri_state, member.distri_brandid, member.member_identity,
                member.trad_amount,member.freeze_trad,member.dis_trad_money,member.bill_type_code,member.bill_type_number,member.bill_bank_branch';
                $where['chain_user.shr_staff_no'] = $data['shr_staff_no'];
                $where['chain_user.store_phone_state'] = 0;
                $where['member.distri_state'] = 2;
                $old_member_info = Model()->table('chain_user,member')->join('left')->on('chain_user.user_mobile = member.member_mobile')
                    ->field($field)->where($where)->find();

                $new_member_info = $model_member->where(['member_mobile' => $data['mobile']])->find();

                $model_member->beginTransaction();
                if ($old_member_info) {
                    if (intval($data['status']) == 2) {
                        $distri_brandid = $old_member_info['distri_brandid'] ?: 0;
                        $distri_chainid = $old_member_info['distri_chainid'] ?: 0;
                        $distri_state   = $old_member_info['distri_state'] ?: 0;

                        //查询员工信息
                        $is_change_position = $data['is_change_position'];
//                        $staff_info = ShrTStaffInfo::getInfo($data['mobile']);
                        if ($data['nc_code']) {//分院
                            $chain_info = Model('chain')->getChainInfo(array('account_id' => $data['nc_code']), "chain_id,chain_name,chain_brand_id");
                            $distri_brandid = $chain_info['chain_brand_id'];
                            $distri_chainid = $chain_info['chain_id'];
                            //判断是否调店
                            if ($distri_chainid != $old_member_info['chain_id']) {
                                $is_change_position = 1;
                            }
                        }

                        //1.判断是否修改手机号
                        if ($old_member_info['user_mobile'] != $data['mobile']) {
                            $member = array();
                            $member['member_truename'] = $member['bill_user_name'] = $data['name'];
                            $member['member_mobile'] = $data['mobile'];
                            $member['member_mobile_bind'] = 1;
                            $member['member_time'] = $member['member_login_time'] = $member['member_old_login_time'] = time();
                            $member['distri_brandid'] = $distri_brandid;
                            $member['distri_chainid'] = $distri_chainid;
                            $member['distri_state'] = $distri_state;
                            $member['member_identity'] = $data['id_card'];
                            $member['trad_amount'] = $new_member_info['trad_amount'] + $old_member_info['trad_amount'];
                            $member['freeze_trad'] = $new_member_info['freeze_trad'] + $old_member_info['freeze_trad'];
                            $member['dis_trad_money'] = $new_member_info['dis_trad_money'] + $old_member_info['dis_trad_money'];
                            $member['bill_type_code'] = $old_member_info['bill_type_code'];
                            $member['bill_type_number'] = $old_member_info['bill_type_number'];
                            $member['bill_bank_name'] = $old_member_info['bill_bank_name'];
                            $member['bill_bank_branch'] = $old_member_info['bill_bank_branch'];
                            //判断新用户是否有记录
                            if ($new_member_info) {
                                $result = $model_member->where(['member_id' => $new_member_info['member_id']])->update($member);
                                if (!$result) {
                                    throw new Exception("new_member_info-更新失败");
                                }
                                $member['member_id'] = $new_member_info['member_id'];
                            } else {
                                $member['scrm_user_id'] = '';
                                $num = substr($data['mobile'], -4);
                                $member['member_name'] = Logic('connect_api')->getMemberName('upet_', $num);
                                $result = $model_member->insert($member);
                                if (!$result) {
                                    throw new Exception("member-添加失败");
                                }
                                $member['member_id'] = $result;
                            }
                            //更新分销订单分销员
                            foreach ([
                                         'dis_pay' => 'dis_member_id',
                                         'dis_trad_cash' => 'tradc_member_id',
                                         'dis_trad_log' => 'lg_member_id',
                                         'distri_member_fans' => ['member_id', 'dis_member_id'],
                                         'distri_outside_member' => ['member_id', 'dis_member_id'],
                                         'vr_order' => 'dis_member_id',
                                         'vr_order_code' => 'dis_member_id',
                                         'order_goods' => 'dis_member_id',
                                     ] as $table => $columns) {

                                foreach ((array)$columns as $column) {
                                    Model()->table($table)
                                        ->where([$column => $old_member_info['member_id']])->update([
                                            $column => $member['member_id']
                                        ]);
                                }
                            }
                            $res = Model('chain_member')->where(['mobile'=>$old_member_info['user_mobile']])->find();
                            if ($res) {
                                $res1 = Model('chain_member')->where(['mobile' => $old_member_info['user_mobile']])
                                    ->update(['chain_id' => $distri_chainid, 'mobile' => $member['member_mobile']]);                            //更新职员信息
                                if (!$res1) {
                                    throw new Exception("3-chain_member-更新失败");
                                }
                            }
                            $update_member = [
                                'trad_amount' => 0,
                                'freeze_trad' => 0,
                                'dis_trad_money' => 0,
                                'distri_state' => 0,
                                'bill_user_name' => '',
                                'bill_bank_name' => '',
                                'bill_type_number' => '',
                                'member_identity' => '',
                                'distri_chainid' => 0,
                                'distri_brandid' => 0,
                                'quit_time' => time(),
                            ];
                            $member_res = $model_member->where(['member_id' => $old_member_info['member_id']])->update($update_member);
                            if (!$member_res) {
                                throw new Exception("1-update_member-更新会员信息失败");
                            }

                            //删除新手机号，保留原员工编码的记录
                            Model('chain_user')->where(['user_mobile' => $data['mobile']])->delete();

                            $this->log('1-更新修改手机号成功,' . $data['mobile']);
                        } else {
                            if ($is_change_position) {
                                $user_old_res = Model('chain')->addChainUserOld([
                                    'user_name' => $data['name'],
                                    'user_mobile' => $data['mobile'],
                                    'chain_id' => $distri_chainid,
                                    'store_id' => 1,
                                    'status' => $data['status'] == 2 ? 1 : 0,
                                    'create_time' => time(),
                                ]);
                                $result = $model_member->where(['member_id' => $new_member_info['member_id']])->update(['distri_chainid' => $distri_chainid]);
                                Model('chain_member')->where(['mobile'=>$data['mobile']])->update(['chain_id'=>$distri_chainid]);
                                if (!$user_old_res || !$result) {
                                    throw new Exception("update_member-调店更新失败");
                                }
                                $this->log('2-调店成功,' . $data['mobile']);
                            }
                        }
                        $model_member->commit();
                        //更新员工信息
                        $chain_user_res = Model('chain_user')->where(['shr_staff_no' => $data['shr_staff_no']])->update([
                            'user_mobile' => $data['mobile'],
                            'chain_id' => $distri_chainid,
                            'isjudge' => 1,
                        ]);
                        if (!$chain_user_res) {
                            throw new Exception("update_user-更新员工信息失败");
                        }
                    } elseif (intval($data['status']) == 16) {//离职
                        $update_member = [
                            'distri_state' => 0,
                            'distri_chainid' => 0,
                            'distri_brandid' => 0,
                            'member_identity' => '',
                            'quit_time' => time(),
                        ];
                        $member_res = $model_member->where(['member_mobile' => $data['mobile']])->update($update_member);
                        if (!$member_res) {
                            throw new Exception("2-update_member-更新会员信息失败");
                        }
                        //删除企业微信门店登录手机记录
                        $res1 = Model('chain_member')->where(['mobile' => $data['mobile']])->delete();
                        if (!$res1){
                            throw new Exception("3-chain_member-删除失败");
                        }
                        $res2 = Model('chain_user')->where(['user_mobile' => $data['mobile']])->delete();
                        if (!$res2){
                            throw new Exception("4-chain_user-删除失败");
                        }
                        $model_member->commit();
                        $this->log('3-离职成功,' . $data['shr_staff_no']);
                    }else{
                        $this->log('无相关操作记录,' . $data['shr_staff_no']);
                    }

                }else{
                    $this->log('4-无该员工记录,' . $data['shr_staff_no']);
                }
                $q->ack($envelope->getDeliveryTag()); //手动发送ACK应答
            } catch (Exception $e) {
                $this->log($e->getMessage() . ',' . $data['shr_staff_no']);
                $model_member->rollback();
            }
            Mysql::close();
        }, 10); // 10秒超时
        $cnn->disconnect();
    }


}