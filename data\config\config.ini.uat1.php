<?php

$config = array();
$config['shop_site_url']        = 'http://localhost';
$config['cms_site_url']         = 'http://localhost/cms';
$config['microshop_site_url']   = 'http://localhost/microshop';
$config['circle_site_url']      = 'http://localhost/circle';
$config['admin_site_url']       = 'http://localhost/admin_seo';
$config['mobile_site_url']      = 'https://uat1.upetmart.rvet.c/mobile';
$config['wap_site_url']         = 'http://localhost/wap';
$config['chat_site_url']        = 'http://localhost/chat';
$config['node_site_url']        = 'http://localhost:8090';
$config['delivery_site_url']    = 'http://localhost/delivery';
$config['chain_site_url']       = 'http://localhost/chain';
$config['member_site_url']      = 'http://localhost/member';
$config['distribute_site_url']  = 'http://localhost/distribute';
$config['upload_site_url']      = 'http://localhost/data/upload';
$config['resource_site_url']    = 'http://localhost/data/resource';
$config['upload_xcx_url']       = 'http://localhost/data/upload';

// 重写url
if ($_SERVER['SERVER_PORT'] > 0 && $_SERVER['SERVER_PORT'] <> '80' || $_SERVER['REQUEST_SCHEME'] == 'https') {
    foreach ($config as $key => $value) {
        // 不包含url的不处理
        if (!is_string($value) || stripos($key, '_site_url') === false ||
            stripos($value, $_SERVER['SERVER_NAME']) === false) {
            continue;
        }

        $ps = parse_url($value);
        // 不是有效url的不处理
        if (empty($ps['scheme']) || empty($ps['host'])) {
            continue;
        }

        $search = $ps['scheme'] . '://' . $ps['host'];
        if ($ps['port']) {
            $search .= ':' . $ps['port'];
        }
        $config[$key] = str_replace($search, $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'], $value);
    }
}

$config['version']              = '2018031514200';
$config['setup_date']           = '2018-09-26 11:09:58';
$config['gip']                  = 0;
$config['dbdriver']             = 'mysql';
$config['tablepre']             = 'upet_';
//$config['db']['master']['dbhost']       = '***********';//old-uat
$config['db']['master']['dbhost']       = '***********';// new-uat
$config['db']['master']['dbport']       = '13306';
$config['db']['master']['dbuser']       = 'root';
$config['db']['master']['dbpwd']        = 'XjIrQepuHn7u^E8D';
$config['db']['master']['dbname']       = 'upetmart';
$config['db']['master']['dbcharset']    = 'UTF-8';
$config['db']['slave']                  = $config['db']['master'];
$config['session_expire']   = 3600;
$config['lang_type']        = 'zh_cn';
$config['cookie_pre']       = '2F15_';
$config['cache_open'] = true;
$config['redis']['prefix']        = 'upet_';
$config['redis']['master']['port']        = 6379;
$config['redis']['master']['host']        = '************';
$config['redis']['master']['pconnect']    = 0;
$config['redis']['master']['auth'] = 'MkdGH*3ldf';
//config['redis']['master']['auth'] = '';
//$config['redis']['slave']             = array();
$config['fullindexer']['open']      = false;
$config['fullindexer']['appname']   = 'shopnc';
$config['debug']            = false;
$config['url_model'] = true;
$config['subdomain_suffix'] = '';
$config['session_type'] = 'redis';
$config['session_save_path'] = 'tcp://************:6379?auth=MkdGH*3ldf';
$config['node_chat'] = true;
$config['shopnc_sn'] = '67ec37fa5686c92f16acd94418784ae1';
//流量记录表数量，为1~10之间的数字，默认为3，数字设置完成后请不要轻易修改，否则可能造成流量统计功能数据错误
$config['flowstat_tablenum'] = 3;
$config['sms']['gwUrl'] = 'http://hprpt2.eucp.b2m.cn:8080/sdk/SDKService';
$config['sms']['serialNumber'] = '';
$config['sms']['password'] = '';
$config['sms']['sessionKey'] = '';
$config['queue']['open'] = false;
$config['queue']['host'] = '************';
$config['queue']['port'] = 6379;
$config['queue']['auth'] = 'MkdGH*3ldf';

//$config['oss']['open'] = false;
$config['oss']['active'] = true;
$config['oss']['img_url'] = 'https://oss.upetmart.com';
$config['oss']['api_url'] = 'oss-accelerate.aliyuncs.com';
$config['oss']['bucket'] = 'upetmart-files';
$config['oss']['access_id'] = 'LTAI4G5Kc5oq9mYnpoTwHxBB';
$config['oss']['access_key'] = '******************************';

$config['zz253']['open'] = true;

$config['erp_url_host'] = 'http://awen.uat.rvet.cn';
$config['erp_platform_deptId'] = '123';

$config['baidu_map_key']='69E08FE28F3eb86cfe0353779f9fddc0';
$config['tengxun_map_key'] = 'XGABZ-SW7KJ-FDFFT-K2IO2-2DNIZ-7FFFG';
$config['https'] = false;
//开店数量限制，0为不限
$config['store_limit'] = 0;
//发商品数量限制，0为不限
$config['sg_goods_limit'] = 0;
$config['promotion_voucher_buyertimes_limit'] = 10;
//判断门店是否开启推荐
$config['chain_goods_list'] = true;//不开启false
//判断门店是否开启优惠卷
$config['chain_voucher'] = true;//不开启false
//是否使用门店价格
$config['open_chain_price'] =false;//不开启false
//门店上传商品权限帐号
$config['chain_user_name'] = 'upet_chain';
//密码
$config['chain_user_pass'] = '123456';

$config['version'] ='test';
$config['is_excelport'] = true;
if(time() >strtotime(date('Y-m-d 10:00:00')) && time()<strtotime(date('Y-m-d 22:00:00'))) {

    $config['is_excelport'] = true;
}
//云服务器MQ配置信息 拆单相关
$config['cloud_mq_host'] = '***********';
$config['cloud_mq_vhost'] = '/';
$config['cloud_mq_port'] = '5672';
$config['cloud_mq_login'] = 'admin';
$config['cloud_mq_password'] = 'UXa85jSUNyuqbiAq';
//数据中心MQ配置信息 核销码相关
$config['dc_mq_host'] = '***********';
//$config['dc_mq_host'] = '**************';
$config['dc_mq_vhost'] = '/';
$config['dc_mq_port'] = '5672';
$config['dc_mq_login'] = 'admin';
$config['dc_mq_password'] = 'UXa85jSUNyuqbiAq';
//手机号合并mq配置信息
$config['dc_merge_host'] = '************';
$config['dc_merge_vhost'] = '/';
$config['dc_merge_port'] = '5672';
$config['cloud_mq_login'] = 'admin';
$config['cloud_mq_password'] = 'UXa85jSUNyuqbiAq';
// $config['vip_goods_ids'] = array(1000046009,1000309002,1000185001);
$config['vip_goods_ids'] = [
    1029877001 => 1,
    1020748001 => 0
];
$config['vip_timeline'] = "1577808000";
$config['pro'] = 'test'; //dev
//电银支付
$config['dianyin_pay'] = true;
$config['dianyin_request_url'] = 'http://awen.uat.rvet.cn';
//$config['dianyin_request_url'] = 'http://awen.rvet.cn';
//$config['dianyin_request_url'] = 'http://127.0.0.1:7035';
$config['dianyin_pay_key'] = 'wkE+ow8O+gj3L7QwqrZFtORTsufXRZtsWl3P9PpXFSKu2RLS7HULMNk93R1lDuVU';
$config['dianyin_pay_key_s2b2c'] = '8rsvLZqCsQ96QH0K+RTpq3PjAkNTgB1n92kKkyq3gi4AStATXyil/Q7edxxJFaWl3P9P';
$config['dianyin_merchant_id'] = 'pb24gQXV0aG9yaXR5M';
$config['dianyin_merchant_id_s2b2c'] = 'AAECggEAcboPccMCaLS';
$config['dianyin_subAppId'] = 'wx80e6a4fd1f79a85b';//wx7d522609bb8c0d8a
$config['dianyin_allow_time'] = '120';
//ERP售后订单地址
$config['erp_after_order_url'] = 'http://awen.uat.rvet.cn';//http://**************:21008 //mall
$config['erp_after_refund_url'] = 'http://awen.uat.rvet.cn';//boss
$config['datacenter_orderpay_url'] = 'http://awen.uat.rvet.cn';//order_api
$config['coupon_request_url'] = 'http://awen.uat.rvet.cn';//数据中心优惠券核销码获取地址
//对外接口key 物流查询用到
$config['openapi_key'] = "52132e92d85005fd13e2a2bd89b7bed4";
//保险种类
$config['insurance_type'] = array(
    array('id' => 1,'gid' => 1019897001, 'title' => "华安宠物责任险", 'type' => 1),
    array('id' => 2,'gid' => 1019896001,'title' => "众安宠物责任险",'type'=>1),
    array('id' => 3,'gid' => 1019895001, 'title' => "众安宠物医疗险", 'type' => 1),
    array('id' => 4,'gid' => 1019898001, 'title' => "众安暖心保意外险", 'type' => 1),
    array('id' => 5,'gid' => 1019899001, 'title' => "华安家庭财产险", 'type' => 2),
    array('id' => 6,'gid' => 1019916001, 'title' => "众安宠物医疗险尊享版", 'type' => 1),
    array('id' => 7,'gid' => 1019917001, 'title' => "众安宠物责任险尊享版", 'type' => 1),
    array('id' => 8,'gid' => 112675, 'title' => "众安宠物医疗险", 'type' => 1),
    array('id' => 9,'gid' => 112674, 'title' => "众安宠物责任险", 'type' => 1)
);
//id值和title值不要随便改
$config['aw_request_url'] = 'http://erp-zuul-outside-pre.rp-field.com';
//$config['aw_request_url'] = 'http://erp-zuul-outside.rp-field.co';
$config['aw_appid'] = ' sz_rppet';
$config['aw_secret'] = 'c4f7351598d1447';
$config['aw_version'] = '*******';

//统一支付加密盐
$config['app_secret_key'] = 'wkE+ow8O+gj3L7QwqrZFtORTsufXRZtsWl3P9PpXFSKu2RLS7HULMNk93R1lDuVU';

$config['bj_mq_host'] = '************';
$config['bj_mq_vhost'] = 'avatarzlerp-pre';
$config['bj_mq_port'] = '5672';
$config['bj_mq_login'] = 'szuser';
$config['bj_mq_password'] = 'k*W6QZTjU6B&';

//shr员工 mq
$config['shr_mq_host'] = '************';
$config['shr_mq_vhost'] = 'avatarzldc-test';
$config['shr_mq_port'] = '5672';
$config['shr_mq_login'] = 'avatarzldc';
$config['shr_mq_password'] = 'avatarzldc';

//统一认证微信asscess_token配置
$config['aw_wechat_url'] = 'http://awen.uat.rvet.cn';
$config['aw_wechat_appid'] = '6';
$config['aw_wechat_secretcode'] = '6tVf97dkhdAHSYTh40HTlwcbISnXBdMs';

$config['rytl_public_key'] = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCNdHGhM23hlEMALxXcNfv8vQMt
2EQQnkrDT6TxcuUHJHlIfGN1SXPb6fIuvYRb/cohAJeZnWKphr9crFgj4XNnImGK
R2mf2xhnGHb7eAOuoh3kL1hqRv6v5lQA3uvTzQ3QfpuIB36mLW4L4NfOX/5EgcCG
ksN0GBx5Y9wDi90gvQIDAQAB
-----END PUBLIC KEY-----';

//连接数据库配置test
$config['db2'] =[
    'datacenter'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'datacenter',
        'charset'=>'utf8',
        'prefix'=>'',
        'resultset_type'=>'collection',
        'debug' => true
    ],
    'datapowercenter'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'datacenter',
        'charset'=>'utf8',
        'prefix'=>'',
        'resultset_type'=>'collection',
        'debug' => true
    ],

    'activity_donate'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'datacenter',
        'charset'=>'utf8',
        'prefix'=>'',
        'resultset_type'=>'collection',
        'debug' => true
    ],
    'dc_product'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'dc_product',
        'charset'=>'utf8',
        'prefix'=>'',
        'resultset_type'=>'collection'

    ],
    'dashboard'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'dashboard',
        'charset'=>'utf8',
        'prefix'=>'dashboard_',
        'resultset_type'=>'collection'
    ],
    'dc_order'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'dc_order',
        'charset'=>'utf8',
        'prefix'=>'',
        'resultset_type'=>'collection'
    ],
    'dc_activity'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'dc_activity',
        'charset'=>'utf8',
        'prefix'=>'',
        'resultset_type'=>'collection'
    ],
    'pay_center'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'pay_center',
        'charset'=>'utf8',
        'prefix'=>'',
        'resultset_type'=>'collection'
    ],
    'dm_mdm'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'dm_mdm',
        'charset'=>'utf8',
        'prefix'=>'',
        'resultset_type'=>'collection'
    ],
    'eshop'=>[
        'type'=>'mysql',
        'hostname'=>'***********',
        'hostport'=>'13306',
        'username'=>'root',
        'password'=>'XjIrQepuHn7u^E8D',
        'database'=>'eshop',
        'charset'=>'utf8',
        'prefix'=>'',
        'resultset_type'=>'collection'
    ],
];

// 微信支付
$config['wechat_pay_v3'] = [
    'mchId' => '1329949101',
    'key' => BASE_ROOT_PATH.'/ssl/wx_cert/1329949101/apiclient_key.pem',
    'cert' => BASE_ROOT_PATH.'/ssl/wx_cert/1329949101/apiclient_cert.pem',
    'apiV3Key' => 'DWNtzMspPazmm4wmqt8Di4uTFTjJ3nSP'
];

// 教育内容安全校验接口
$config['zhiyue_url'] = 'http://uat.zhiyueh5.rvet.cn/api';

return $config;
